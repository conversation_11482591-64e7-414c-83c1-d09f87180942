import typescript from '@rollup/plugin-typescript';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import dts from 'rollup-plugin-dts';
import filesize from 'rollup-plugin-filesize';

const external = [
  '@modelcontextprotocol/sdk',
  'axios',
  'uuid',
  'fs',
  'path',
  'url',
  'child_process',
  'os',
  'crypto',
  'util',
  'stream',
  'events',
  'net',
  'tls',
  'http',
  'https',
  'zlib',
  'querystring'
];

export default [
  // ES Module bundle (production optimized)
  {
    input: 'mcp/src/index.ts',
    output: [
      {
        file: 'dist/mcp/index.js',
        format: 'es',
        sourcemap: false,
        banner: '#!/usr/bin/env node',
        compact: true
      }
    ],
    external,
    treeshake: {
      moduleSideEffects: false,
      propertyReadSideEffects: false,
      unknownGlobalSideEffects: false
    },
    plugins: [
      resolve({
        preferBuiltins: true,
        exportConditions: ['node'],
        browser: false
      }),
      commonjs({
        ignoreDynamicRequires: true,
        include: /node_modules/
      }),
      json(),
      typescript({
        tsconfig: 'mcp/tsconfig.json',
        outDir: 'dist/mcp',
        declaration: false,
        sourceMap: false,
        compilerOptions: {
          target: 'ES2022',
          module: 'ESNext',
          moduleResolution: 'Bundler',
          declaration: false,
          removeComments: true,
          stripInternal: true,
          inlineSourceMap: false,
          inlineSources: false
        }
      }),
      filesize({
        showMinifiedSize: false,
        showGzippedSize: true,
        showBrotliSize: false
      })
    ],
    onwarn(warning, warn) {
      // 生产环境下忽略所有警告以保持输出清洁
      if (warning.code === 'THIS_IS_UNDEFINED') return;
      if (warning.code === 'CIRCULAR_DEPENDENCY') return;
      if (warning.code === 'UNUSED_EXTERNAL_IMPORT') return;
      warn(warning);
    }
  },
  // CommonJS bundle (for require support)
  {
    input: 'mcp/src/index.ts',
    output: [
      {
        file: 'dist/mcp/index.cjs',
        format: 'cjs',
        sourcemap: false,
        compact: true,
        exports: 'named'
      }
    ],
    external,
    treeshake: {
      moduleSideEffects: false,
      propertyReadSideEffects: false,
      unknownGlobalSideEffects: false
    },
    plugins: [
      resolve({
        preferBuiltins: true,
        exportConditions: ['node']
      }),
      commonjs({
        ignoreDynamicRequires: true
      }),
      json(),
      typescript({
        tsconfig: 'mcp/tsconfig.json',
        outDir: 'dist/mcp',
        declaration: false,
        sourceMap: false,
        compilerOptions: {
          target: 'ES2022',
          module: 'ESNext', // 让 Rollup 处理模块转换
          moduleResolution: 'Bundler',
          declaration: false,
          removeComments: true,
          stripInternal: true,
          inlineSourceMap: false,
          inlineSources: false
        }
      }),
      filesize({
        showMinifiedSize: false,
        showGzippedSize: true,
        showBrotliSize: false
      })
    ],
    onwarn(warning, warn) {
      // 生产环境下忽略所有警告以保持输出清洁
      if (warning.code === 'THIS_IS_UNDEFINED') return;
      if (warning.code === 'CIRCULAR_DEPENDENCY') return;
      if (warning.code === 'UNUSED_EXTERNAL_IMPORT') return;
      warn(warning);
    }
  },
  // TypeScript declarations bundle
  {
    input: 'mcp/src/index.ts',
    output: {
      file: 'dist/mcp/index.d.ts',
      format: 'es'
    },
    external,
    plugins: [
      dts({
        tsconfig: 'mcp/tsconfig.json',
        compilerOptions: {
          declaration: true,
          emitDeclarationOnly: true
        }
      })
    ]
  }
]; 