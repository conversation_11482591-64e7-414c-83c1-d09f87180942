{"name": "@xhs/codemind", "version": "1.0.23", "description": "codemind sdk", "main": "dist/mcp/index.js", "module": "dist/mcp/index.js", "types": "dist/mcp/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/mcp/index.js", "require": "./dist/mcp/index.cjs", "types": "./dist/mcp/index.d.ts"}}, "scripts": {"test": "test", "clean": "rm -rf dist", "install:mcp": "cd mcp && npm install", "build:mcp": "cd mcp && npm run build", "copy:mcp": "mkdir -p dist/mcp && cp -r mcp/build/* dist/mcp/ && cp -f index.d.ts dist/ || true", "setup:python": "python3 -m venv .venv && . .venv/bin/activate && pip3 install -r requirements.txt", "inspect:mcp": "npx @modelcontextprotocol/inspector node ./dist/mcp/index.js", "build:dev": "npm run clean && npm run install:mcp && npm run build:mcp && npm run copy:mcp", "build:prod": "npm run clean && npm run install:mcp && rollup -c rollup.config.prod.js && chmod +x dist/mcp/index.js && npm run copy:assets", "copy:assets": "cp -f index.d.ts dist/ || true", "build": "npm run build:prod", "build:watch": "rollup -c -w", "analyze": "NODE_ENV=production rollup -c --bundleConfigAsCjs", "prepublishOnly": "npm run build"}, "files": ["dist", "python_dist"], "repository": {"type": "git", "url": "codemind"}, "keywords": ["codemind"], "author": "codemind", "license": "ISC", "devDependencies": {"typescript": "^5.7.2", "rollup": "^4.9.0", "@rollup/plugin-typescript": "^12.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.1.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-filesize": "^10.0.0", "tslib": "^2.8.1"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "axios": "^1.9.0", "uuid": "^11.1.0"}}