import typescript from '@rollup/plugin-typescript';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import dts from 'rollup-plugin-dts';
import filesize from 'rollup-plugin-filesize';

const isProduction = process.env.NODE_ENV === 'production';

const external = [
  '@modelcontextprotocol/sdk',
  'axios',
  'uuid',
  'fs',
  'path',
  'url',
  'child_process',
  'os',
  'crypto',
  'util',
  'stream',
  'events',
  'net',
  'tls',
  'http',
  'https',
  'zlib',
  'querystring'
];

export default [
  // JavaScript bundle
  {
    input: 'mcp/src/index.ts',
    output: [
      {
        file: 'dist/mcp/index.js',
        format: 'es',
        sourcemap: !isProduction,
        banner: '#!/usr/bin/env node',
        compact: isProduction
      }
    ],
    external,
    plugins: [
      resolve({
        preferBuiltins: true,
        exportConditions: ['node']
      }),
      commonjs(),
      json(),
      typescript({
        tsconfig: 'mcp/tsconfig.json',
        outDir: 'dist/mcp',
        declaration: false, // 由dts插件处理
        sourceMap: !isProduction,
        compilerOptions: {
          target: 'ES2022',
          module: 'ESNext',
          moduleResolution: 'Bundler',
          declaration: false,
          removeComments: isProduction,
          stripInternal: isProduction
        }
      }),
      filesize({
        showMinifiedSize: false,
        showGzippedSize: true
      })
    ],
    onwarn(warning, warn) {
      // 忽略 "this is undefined" 警告
      if (warning.code === 'THIS_IS_UNDEFINED') return;
      // 忽略循环依赖警告
      if (warning.code === 'CIRCULAR_DEPENDENCY') return;
      warn(warning);
    }
  },
  // TypeScript declarations bundle
  {
    input: 'mcp/src/index.ts',
    output: {
      file: 'dist/mcp/index.d.ts',
      format: 'es'
    },
    external,
    plugins: [
      dts({
        tsconfig: 'mcp/tsconfig.json',
        compilerOptions: {
          declaration: true,
          emitDeclarationOnly: true
        }
      })
    ]
  }
]; 